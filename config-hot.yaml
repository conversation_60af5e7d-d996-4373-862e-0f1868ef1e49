app:
  name: mono-loan
  port: 8084
  host:
  jwt_issuer: "mono-loan"
  jwt_secret: "secret"
  client_id: xxx
  onesignal_api_key: 123456
  force_update_key: xxx
database:
  host: mono-loan-db
  port: 5432
  user: mono-loan
  pass: mono-loan
  name: mono-loan
allows:
  methods:
  - GET
  - POST
  - PUT
  - PATCH
  - DELETE
  - OPTIONS
  headers:
  - Content-Type
  - Authorization
  - X-CSRF-Token
  - data-api-key
  origins:
    - http://localhost:8000
    - http://localhost:9000
    - http://localhost:4040
    - http://localhost:3000
aktif_bank:
  base_url: "https://apiuat.aktifbank.com.tr/apigateway"
  timeout_seconds: 30
  default_language: "tr"
  sub_channel: ""
