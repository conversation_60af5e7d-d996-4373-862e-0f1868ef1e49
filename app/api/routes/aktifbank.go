package routes

import (
	"errors"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/mono-payments/mono-loan/pkg/domains/aktifbank"
	"github.com/mono-payments/mono-loan/pkg/dtos"
	"github.com/mono-payments/mono-loan/pkg/middleware"
)

func AktifBankRoutes(r *gin.RouterGroup, service aktifbank.Service) {
	group := r.Group("/aktifbank", middleware.FromClient(), middleware.Authorized())
	{
		group.POST("/login", login(service))
		group.POST("/limits", checkLimit(service))
		group.POST("/payment-plans", paymentPlan(service))
		group.POST("/transactions", startTransaction(service))
		group.POST("/refunds", refund(service))
		group.POST("/applications/status", creditStatus(service))
	}
}

// @Summary Aktif Bank oturum açma
// @Description Login servisi ile session ID üretir
// @Tags AktifBank
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param request body dtos.AktifBankLoginRequest true "Login request"
// @Success 200 {object} dtos.AktifBankLoginResponse
// @Failure 400 {object} map[string]any
// @Failure 502 {object} map[string]any
// @Router /aktifbank/login [post]
func login(service aktifbank.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		var req dtos.AktifBankLoginRequest
		if err := c.ShouldBindJSON(&req); err != nil {
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}

		resp, err := service.Login(c.Request.Context(), &req)
		if err != nil {
			handleAktifBankError(c, err)
			return
		}

		c.JSON(http.StatusOK, resp)
	}
}

// @Summary Aktif Bank limit sorgulama
// @Description Müşteri limit ve vade bilgilerini döner
// @Tags AktifBank
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param request body dtos.AktifBankCheckLimitAvailabilityRequest true "Limit request"
// @Success 200 {object} dtos.AktifBankCheckLimitAvailabilityResponse
// @Failure 400 {object} map[string]any
// @Failure 502 {object} map[string]any
// @Router /aktifbank/limits [post]
func checkLimit(service aktifbank.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		var req dtos.AktifBankCheckLimitAvailabilityRequest
		if err := c.ShouldBindJSON(&req); err != nil {
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}

		resp, err := service.CheckLimitAvailability(c.Request.Context(), &req)
		if err != nil {
			handleAktifBankError(c, err)
			return
		}

		c.JSON(http.StatusOK, resp)
	}
}

// @Summary Aktif Bank ödeme planları
// @Description Verilen kredi tutarı için ödeme planı listeler
// @Tags AktifBank
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param request body dtos.AktifBankPaymentPlanRequest true "Payment plan request"
// @Success 200 {object} dtos.AktifBankPaymentPlanResponse
// @Failure 400 {object} map[string]any
// @Failure 502 {object} map[string]any
// @Router /aktifbank/payment-plans [post]
func paymentPlan(service aktifbank.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		var req dtos.AktifBankPaymentPlanRequest
		if err := c.ShouldBindJSON(&req); err != nil {
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}

		resp, err := service.GetPaymentPlan(c.Request.Context(), &req)
		if err != nil {
			handleAktifBankError(c, err)
			return
		}

		c.JSON(http.StatusOK, resp)
	}
}

// @Summary Aktif Bank kredi başvuru başlatma
// @Description Banka başvurusunu başlatır ve yönlendirme linki döner
// @Tags AktifBank
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param request body dtos.AktifBankStartTransactionRequest true "Start transaction request"
// @Success 200 {object} dtos.AktifBankStartTransactionResponse
// @Failure 400 {object} map[string]any
// @Failure 502 {object} map[string]any
// @Router /aktifbank/transactions [post]
func startTransaction(service aktifbank.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		var req dtos.AktifBankStartTransactionRequest
		if err := c.ShouldBindJSON(&req); err != nil {
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}

		resp, err := service.StartTransaction(c.Request.Context(), &req)
		if err != nil {
			handleAktifBankError(c, err)
			return
		}

		c.JSON(http.StatusOK, resp)
	}
}

// @Summary Aktif Bank kredi iadesi
// @Description Belirtilen başvuru için kredi iade talebi oluşturur
// @Tags AktifBank
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param request body dtos.AktifBankRefundRequest true "Refund request"
// @Success 200 {object} dtos.AktifBankRefundResponse
// @Failure 400 {object} map[string]any
// @Failure 502 {object} map[string]any
// @Router /aktifbank/refunds [post]
func refund(service aktifbank.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		var req dtos.AktifBankRefundRequest
		if err := c.ShouldBindJSON(&req); err != nil {
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}

		resp, err := service.Refund(c.Request.Context(), &req)
		if err != nil {
			handleAktifBankError(c, err)
			return
		}

		c.JSON(http.StatusOK, resp)
	}
}

// @Summary Aktif Bank kredi durumu
// @Description Başvuru veya iade durumunu sorgular
// @Tags AktifBank
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param request body dtos.AktifBankCreditStatusRequest true "Credit status request"
// @Success 200 {object} dtos.AktifBankCreditStatusResponse
// @Failure 400 {object} map[string]any
// @Failure 502 {object} map[string]any
// @Router /aktifbank/applications/status [post]
func creditStatus(service aktifbank.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		var req dtos.AktifBankCreditStatusRequest
		if err := c.ShouldBindJSON(&req); err != nil {
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}

		resp, err := service.GetCreditStatus(c.Request.Context(), &req)
		if err != nil {
			handleAktifBankError(c, err)
			return
		}

		c.JSON(http.StatusOK, resp)
	}
}

func handleAktifBankError(c *gin.Context, err error) {
	var apiErr *aktifbank.APIError
	if errors.As(err, &apiErr) {
		c.AbortWithStatusJSON(apiErr.StatusCode, gin.H{
			"error":   "aktifbank request failed",
			"details": apiErr.Body,
		})
		return
	}

	c.AbortWithStatusJSON(http.StatusBadGateway, gin.H{
		"error":   "aktifbank integration error",
		"details": err.Error(),
	})
}
