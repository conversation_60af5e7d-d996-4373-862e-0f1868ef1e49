package routes

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/mono-payments/mono-loan/pkg/domains/bankaggregator"
	"github.com/mono-payments/mono-loan/pkg/dtos"
	"github.com/mono-payments/mono-loan/pkg/middleware"
)

func BankAggregationRoutes(r *gin.RouterGroup, service bankaggregator.Service) {
	group := r.Group("/banks", middleware.FromClient())
	{
		group.POST("/offers", fetchOffers(service))
	}
}

func fetchOffers(service bankaggregator.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		var req dtos.MultiBankOffersRequest
		if err := c.ShouldBind<PERSON>(&req); err != nil {
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}

		resp, err := service.FetchOffers(c.Request.Context(), &req)
		if err != nil {
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": err.<PERSON>r()})
			return
		}

		c.JSON(http.StatusOK, resp)
	}
}
