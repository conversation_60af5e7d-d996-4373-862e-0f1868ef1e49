version: "3"
services:
  mono-loan-db:
    image: "postgres:14.6"
    container_name: mono-loan-db
    volumes:
      - mono-loan_data:/var/lib/postgresql/data
    networks:
      - mono
    restart: always
    ports:
      - "5433:5432"
    environment:
      - POSTGRES_USER=mono-loan
      - POSTGRES_PASSWORD=mono-loan
      - POSTGRES_DB=mono-loan

  mono-loan:
    build:
      context: .
      dockerfile: Dockerfile.dev
    image: mono-loan
    environment:
      - TZ="Europe/Istanbul"
    container_name: mono-loan
    restart: always
    networks:
      - mono
    volumes:
      - ./:/app
      - ./config-hot.yaml:/app/config.yaml
    ports:
      - 8084:8084
    depends_on:
      - mono-loan-db
  


volumes:
  mono-loan_data:

networks:
  mono:
    driver: bridge