package server

import (
	"fmt"
	"log"
	"net"
	"net/http"
	"os"
	"time"

	"github.com/Depado/ginprom"
	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
	"github.com/mono-payments/mono-loan/app/api/routes"
	"github.com/mono-payments/mono-loan/docs"
	"github.com/mono-payments/mono-loan/pkg/config"
	"github.com/mono-payments/mono-loan/pkg/database"
	"github.com/mono-payments/mono-loan/pkg/domains/aktifbank"
	"github.com/mono-payments/mono-loan/pkg/domains/bankaggregator"
	"github.com/mono-payments/mono-loan/pkg/middleware"
	swaggerFiles "github.com/swaggo/files"
	ginSwagger "github.com/swaggo/gin-swagger"
	"go.opentelemetry.io/contrib/instrumentation/github.com/gin-gonic/gin/otelgin"
)

var (
	swaggerUser string
	swaggerPass string
)

func LaunchHttpServer(cfg *config.Config) {
	log.Println("Starting HTTP Server...")
	gin.SetMode(gin.ReleaseMode)

	app := gin.New()
	app.Use(gin.LoggerWithFormatter(func(log gin.LogFormatterParams) string {
		return fmt.Sprintf("[%s] - %s \"%s %s %s %d %s\"\n",
			log.TimeStamp.Format("2006-01-02 15:04:05"),
			log.ClientIP,
			log.Method,
			log.Path,
			log.Request.Proto,
			log.StatusCode,
			log.Latency,
		)
	}))
	app.Use(gin.Recovery())
	app.Use(otelgin.Middleware(cfg.App.Name))
	app.Use(middleware.ClaimIp())

	//app.Use(middleware.Secure())
	app.Use(cors.New(cors.Config{
		AllowMethods:     cfg.Allows.Methods,
		AllowHeaders:     cfg.Allows.Headers,
		AllowOrigins:     cfg.Allows.Origins,
		AllowCredentials: false,
		MaxAge:           12 * time.Hour,
	}))

	p := ginprom.New(
		ginprom.Engine(app),
		ginprom.Subsystem("gin"),
		ginprom.Path("/metrics"),
		ginprom.Ignore("/swagger/*any"),
	)
	app.Use(p.Instrument())

	// Ensure database is initialised for other layers even if not used directly here.
	database.DBClient()

	// -----> Routes Start
	api := app.Group("/api/v1")

	aktifRepo := aktifbank.NewRepository(cfg.AktifBank)
	aktifService := aktifbank.NewService(aktifRepo, cfg.AktifBank)
	routes.AktifBankRoutes(api, aktifService)

	bankAggregatorService := bankaggregator.NewService(
		bankaggregator.NewAktifbankAdapter(aktifService),
		bankaggregator.NewMonoBankAdapter(),
	)
	routes.BankAggregationRoutes(api, bankAggregatorService)

	// Routes End <-----

	app.GET("/docs", func(c *gin.Context) {
		c.Redirect(http.StatusMovedPermanently, "docs/index.html")
	})
	if os.Getenv("SWAGGER_USER") != "" {
		swaggerUser = os.Getenv("SWAGGER_USER")
	} else {
		swaggerUser = "mono-loan-dev"
	}
	if os.Getenv("SWAGGER_PASS") != "" {
		swaggerPass = os.Getenv("SWAGGER_PASS")
	} else {
		swaggerPass = "mono-loan-dev"
	}

	docs.SwaggerInfo.Host = cfg.App.BaseUrl
	docs.SwaggerInfo.Version = os.Getenv("APP_VERSION")
	app.GET("/docs/*any",
		gin.BasicAuth(gin.Accounts{
			swaggerUser: swaggerPass,
		}),
		ginSwagger.WrapHandler(swaggerFiles.Handler),
	)

	fmt.Println("Server is running on port " + cfg.App.Port)
	app.Run(net.JoinHostPort(cfg.App.Host, cfg.App.Port))
}
