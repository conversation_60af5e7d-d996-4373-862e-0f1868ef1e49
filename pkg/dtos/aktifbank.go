package dtos

import "encoding/json"

type AktifBankLoginRequest struct {
	Language           string `json:"LANGUAGE,omitempty"`
	ClientSessionID    string `json:"CLIENT_SESSION_ID" binding:"required"`
	SubChannelReserved string `json:"SUB_CHANNEL_RESERVED,omitempty"`
}

type AktifBankLoginResponse struct {
	SessionID string `json:"SESSION_ID"`
}

type AktifBankCheckLimitAvailabilityRequest struct {
	SessionID           string   `json:"SESSION_ID" binding:"required"`
	ClientSessionID     string   `json:"CLIENT_SESSION_ID" binding:"required"`
	ClientTrxIDReserved *string  `json:"CLIENT_TRX_ID_RESERVED,omitempty"`
	ReferenceID         string   `json:"ReferenceId" binding:"required"`
	NationalIDNumber    string   `json:"NationalIdentityNumber" binding:"required"`
	GsmNumber           string   `json:"GsmNumber" binding:"required"`
	TotalPrice          *float64 `json:"TotalPrice,omitempty"`
	BirthDate           string   `json:"BirthDate" binding:"required"`
	MonthlyIncome       *float64 `json:"MonthlyIncome,omitempty"`
}

type AktifBankCheckLimitAvailabilityResponse struct {
	ResponseCode                string      `json:"ResponseCode"`
	ResponseMessage             string      `json:"ResponseMessage"`
	PreApprovedApplicationID    json.Number `json:"PreApprovedApplicationId"`
	AvailableLimit              json.Number `json:"AvailableLimit"`
	MinInterestRate             json.Number `json:"MinInterestRate"`
	MaxInterestRate             json.Number `json:"MaxInterestRate"`
	MaxTerm                     json.Number `json:"MaxTerm"`
	MinAmount                   json.Number `json:"MinAmount"`
	IsNewCustomer               bool        `json:"IsNewCustomer"`
	IsSuccess                   bool        `json:"IsSuccess"`
	InstallmentAmountForMaxTerm json.Number `json:"InstallmentAmountForMaxTerm"`
	CoreTrxIDReserved           string      `json:"CORE_TRX_ID_RESERVED,omitempty"`
}

type AktifBankPaymentPlanRequest struct {
	SessionID           string  `json:"SESSION_ID" binding:"required"`
	ClientSessionID     string  `json:"CLIENT_SESSION_ID" binding:"required"`
	ClientTrxIDReserved *string `json:"CLIENT_TRX_ID_RESERVED,omitempty"`
	ReferenceID         string  `json:"ReferenceId" binding:"required"`
	PreApprovedAppID    string  `json:"PreApprovedApplicationId" binding:"required"`
	NationalIDNumber    string  `json:"NationalIdentityNumber" binding:"required"`
	TotalPrice          float64 `json:"TotalPrice" binding:"required"`
	InstallmentDate     *string `json:"InstallmentDate,omitempty"`
}

type AktifBankPaymentPlanResponse struct {
	CoreTrxIDReserved string                       `json:"CORE_TRX_ID_RESERVED,omitempty"`
	Type              string                       `json:"Type"`
	ResponseCode      string                       `json:"ResponseCode"`
	ResponseMessage   string                       `json:"ResponseMessage"`
	PaymentPlan       []AktifBankPaymentPlanOption `json:"PaymentPlan"`
}

type AktifBankPaymentPlanOption struct {
	LoanAmount              json.Number `json:"LoanAmount"`
	CreditInsuranceFee      json.Number `json:"CreditInsuranceFee"`
	PaymentDate             json.Number `json:"PaymentDate"`
	AnnualEffectiveInterest json.Number `json:"AnnualEffectiveInterestRate"`
	InstallmentAmount       json.Number `json:"InstallmentAmount"`
	Term                    json.Number `json:"Term"`
	InterestRate            json.Number `json:"InterestRate"`
	CreditUsageFee          json.Number `json:"CreditUsageFee"`
	TotalPaymentAmount      json.Number `json:"TotalPaymentAmount"`
}

type AktifBankStartTransactionRequest struct {
	SessionID           string  `json:"SESSION_ID" binding:"required"`
	ClientSessionID     string  `json:"CLIENT_SESSION_ID" binding:"required"`
	ClientTrxIDReserved *string `json:"CLIENT_TRX_ID_RESERVED,omitempty"`
	ReferenceID         string  `json:"ReferenceId" binding:"required"`
	PreApprovedAppID    string  `json:"PreApprovedApplicationId" binding:"required"`
	NationalIDNumber    string  `json:"NationalIdentityNumber" binding:"required"`
	GsmNumber           string  `json:"GsmNumber" binding:"required"`
	TotalPrice          float64 `json:"TotalPrice" binding:"required"`
	CallbackURL         string  `json:"CallbackUrl" binding:"required"`
	TimeToLive          int     `json:"TimeToLive" binding:"required"`
	TotalTerm           int     `json:"TotalTerm" binding:"required"`
	Type                *string `json:"Type,omitempty"`
	PlatformID          *int    `json:"PlatformId,omitempty"`
}

type AktifBankStartTransactionResponse struct {
	CoreTrxIDReserved string      `json:"CORE_TRX_ID_RESERVED,omitempty"`
	ResponseCode      string      `json:"ResponseCode"`
	ResponseMessage   string      `json:"ResponseMessage"`
	ApplicationID     json.Number `json:"ApplicationId"`
	ApplicationDate   json.Number `json:"ApplicationDate"`
	BankRedirectURL   string      `json:"BankRedirectionUrl"`
	IsApproved        bool        `json:"IsApproved"`
}

type AktifBankRefundRequest struct {
	SessionID           string  `json:"SESSION_ID" binding:"required"`
	ClientSessionID     string  `json:"CLIENT_SESSION_ID" binding:"required"`
	ClientTrxIDReserved *string `json:"CLIENT_TRX_ID_RESERVED,omitempty"`
	ReferenceID         string  `json:"ReferenceId" binding:"required"`
	ApplicationID       string  `json:"ApplicationId" binding:"required"`
	ApplicationDate     *string `json:"ApplicationDate,omitempty"`
	RefundAmount        float64 `json:"RefundAmount" binding:"required"`
	RefundType          string  `json:"RefundType" binding:"required,oneof=FULL PARTIAL"`
}

type AktifBankRefundResponse struct {
	CoreTrxIDReserved string      `json:"CORE_TRX_ID_RESERVED,omitempty"`
	ResponseCode      string      `json:"ResponseCode"`
	ResponseMessage   string      `json:"ResponseMessage"`
	BankReferenceID   json.Number `json:"BankReferenceId"`
	IsSuccess         bool        `json:"IsSuccess"`
}

type AktifBankCreditStatusRequest struct {
	SessionID       string  `json:"SESSION_ID" binding:"required"`
	ClientSessionID string  `json:"CLIENT_SESSION_ID" binding:"required"`
	ApplicationID   string  `json:"ApplicationId" binding:"required"`
	Date            *string `json:"Date,omitempty"`
	IdentityNumber  *string `json:"IdentityNumber,omitempty"`
}

type AktifBankCreditStatusResponse struct {
	ApplicationID          json.Number                             `json:"ApplicationId"`
	IsApproved             bool                                    `json:"IsApproved"`
	ApplicationDate        json.Number                             `json:"ApplicationDate"`
	IsNewCustomer          bool                                    `json:"IsNewCustomer"`
	IsDobCompleted         bool                                    `json:"IsDobCompleted"`
	ResponseCode           json.Number                             `json:"ResponseCode"`
	ResponseMessage        string                                  `json:"ResponseMessage"`
	Iban                   string                                  `json:"Iban"`
	NationalIdentityNumber json.Number                             `json:"NationalIdentityNumber"`
	RefundInformation      *AktifBankCreditStatusRefundInformation `json:"RefundInformation,omitempty"`
	PaymentPlan            *AktifBankCreditStatusPaymentPlan       `json:"PaymentPlan,omitempty"`
	CoreTrxIDReserved      string                                  `json:"CORE_TRX_ID_RESERVED,omitempty"`
	AllocationInformation  *AktifBankCreditStatusAllocation        `json:"AllocationInformation,omitempty"`
}

type AktifBankCreditStatusRefundInformation struct {
	TotalRefundAmount json.Number `json:"TotalRefundAmount"`
}

type AktifBankCreditStatusPaymentPlan struct {
	LendingAmount           json.Number `json:"LendingAmount"`
	UsedAmount              json.Number `json:"UsedAmount"`
	InterestRate            json.Number `json:"InterestRate"`
	Term                    json.Number `json:"Term"`
	TotalPaymentAmount      json.Number `json:"TotalPaymentAmount"`
	AnnualEffectiveInterest json.Number `json:"AnnualEffectiveInterestRate"`
	CreditInsuranceFee      json.Number `json:"CreditInsuranceFee"`
	CreditUsageFee          json.Number `json:"CreditUsageFee"`
	InstallAmount           json.Number `json:"InstallAmount"`
}

type AktifBankCreditStatusAllocation struct {
	IsSuccess      bool        `json:"IsSuccess"`
	AllocationDate json.Number `json:"AllocationDate"`
}
