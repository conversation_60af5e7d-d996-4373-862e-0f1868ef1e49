package dtos

import "encoding/json"

type MultiBankOffersRequest struct {
	Banks []MultiBankRequestItem `json:"banks" binding:"required,min=1,dive"`
}

type MultiBankRequestItem struct {
	BankCode    string          `json:"bank_code" binding:"required"`
	Limit       json.RawMessage `json:"limit,omitempty"`
	PaymentPlan json.RawMessage `json:"payment_plan,omitempty"`
}

type MultiBankOffersResponse struct {
	Results []BankOfferResult `json:"results"`
}

type BankOfferResult struct {
	BankCode string                 `json:"bank_code"`
	BankName string                 `json:"bank_name"`
	Limit    *NormalizedLimit       `json:"limit,omitempty"`
	Plan     *NormalizedPaymentPlan `json:"payment_plan,omitempty"`
	Error    *BankError             `json:"error,omitempty"`
}

type BankError struct {
	Message string `json:"message"`
}

type NormalizedLimit struct {
	PreApprovedApplicationID string  `json:"pre_approved_application_id,omitempty"`
	AvailableLimit           float64 `json:"available_limit"`
	MinInterestRate          float64 `json:"min_interest_rate,omitempty"`
	MaxInterestRate          float64 `json:"max_interest_rate,omitempty"`
	MaxTerm                  int     `json:"max_term,omitempty"`
	MinAmount                float64 `json:"min_amount,omitempty"`
	InstallmentAmountForMax  float64 `json:"installment_amount_for_max_term,omitempty"`
	IsNewCustomer            bool    `json:"is_new_customer"`
	ResponseCode             string  `json:"response_code,omitempty"`
	ResponseMessage          string  `json:"response_message,omitempty"`
	IsSuccess                bool    `json:"is_success"`
}

type NormalizedPaymentPlan struct {
	Type            string                        `json:"type,omitempty"`
	ResponseCode    string                        `json:"response_code,omitempty"`
	ResponseMessage string                        `json:"response_message,omitempty"`
	Options         []NormalizedPaymentPlanOption `json:"options"`
}

type NormalizedPaymentPlanOption struct {
	Term                    int     `json:"term"`
	InstallmentAmount       float64 `json:"installment_amount"`
	TotalPaymentAmount      float64 `json:"total_payment_amount"`
	InterestRate            float64 `json:"interest_rate"`
	AnnualEffectiveInterest float64 `json:"annual_effective_interest"`
	LoanAmount              float64 `json:"loan_amount"`
	CreditInsuranceFee      float64 `json:"credit_insurance_fee"`
	CreditUsageFee          float64 `json:"credit_usage_fee"`
}
