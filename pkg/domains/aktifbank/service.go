package aktifbank

import (
	"context"
	"strings"

	"github.com/mono-payments/mono-loan/pkg/config"
	"github.com/mono-payments/mono-loan/pkg/dtos"
)

type Service interface {
	Login(ctx context.Context, req *dtos.AktifBankLoginRequest) (*dtos.AktifBankLoginResponse, error)
	CheckLimitAvailability(ctx context.Context, req *dtos.AktifBankCheckLimitAvailabilityRequest) (*dtos.AktifBankCheckLimitAvailabilityResponse, error)
	GetPaymentPlan(ctx context.Context, req *dtos.AktifBankPaymentPlanRequest) (*dtos.AktifBankPaymentPlanResponse, error)
	StartTransaction(ctx context.Context, req *dtos.AktifBankStartTransactionRequest) (*dtos.AktifBankStartTransactionResponse, error)
	Refund(ctx context.Context, req *dtos.AktifBankRefundRequest) (*dtos.AktifBankRefundResponse, error)
	GetCreditStatus(ctx context.Context, req *dtos.AktifBankCreditStatusRequest) (*dtos.AktifBankCreditStatusResponse, error)
}

type service struct {
	repository Repository
	cfg        config.AktifBank
}

func NewService(repo Repository, cfg config.AktifBank) Service {
	return &service{
		repository: repo,
		cfg:        cfg,
	}
}

func (s *service) Login(ctx context.Context, req *dtos.AktifBankLoginRequest) (*dtos.AktifBankLoginResponse, error) {
	payload := *req
	if payload.Language == "" {
		payload.Language = s.cfg.DefaultLanguage
	}
	if payload.SubChannelReserved == "" {
		payload.SubChannelReserved = s.cfg.SubChannel
	}
	if payload.Language != "" {
		payload.Language = strings.TrimSpace(payload.Language)
	}
	if payload.SubChannelReserved != "" {
		payload.SubChannelReserved = strings.TrimSpace(payload.SubChannelReserved)
	}
	return s.repository.Login(ctx, &payload)
}

func (s *service) CheckLimitAvailability(ctx context.Context, req *dtos.AktifBankCheckLimitAvailabilityRequest) (*dtos.AktifBankCheckLimitAvailabilityResponse, error) {
	return s.repository.CheckLimitAvailability(ctx, req)
}

func (s *service) GetPaymentPlan(ctx context.Context, req *dtos.AktifBankPaymentPlanRequest) (*dtos.AktifBankPaymentPlanResponse, error) {
	return s.repository.GetPaymentPlan(ctx, req)
}

func (s *service) StartTransaction(ctx context.Context, req *dtos.AktifBankStartTransactionRequest) (*dtos.AktifBankStartTransactionResponse, error) {
	return s.repository.StartTransaction(ctx, req)
}

func (s *service) Refund(ctx context.Context, req *dtos.AktifBankRefundRequest) (*dtos.AktifBankRefundResponse, error) {
	return s.repository.Refund(ctx, req)
}

func (s *service) GetCreditStatus(ctx context.Context, req *dtos.AktifBankCreditStatusRequest) (*dtos.AktifBankCreditStatusResponse, error) {
	return s.repository.GetCreditStatus(ctx, req)
}
