package aktifbank

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"

	"github.com/mono-payments/mono-loan/pkg/config"
	"github.com/mono-payments/mono-loan/pkg/dtos"
)

const (
	loginPath            = "/ECommerceCredits/Login"
	limitPath            = "/ECommerceCredits/CheckLimitAvailability"
	paymentPlanPath      = "/ECommerceCredits/GetPaymentPlan"
	startTransactionPath = "/ECommerceCredits/StartTransaction"
	refundPath           = "/ECommerceCredits/Refund"
	creditStatusPath     = "/ECommerceCredits/GetCreditStatus"
)

type Repository interface {
	Login(ctx context.Context, req *dtos.AktifBankLoginRequest) (*dtos.AktifBankLoginResponse, error)
	CheckLimitAvailability(ctx context.Context, req *dtos.AktifBankCheckLimitAvailabilityRequest) (*dtos.AktifBankCheckLimitAvailabilityResponse, error)
	GetPaymentPlan(ctx context.Context, req *dtos.AktifBankPaymentPlanRequest) (*dtos.AktifBankPaymentPlanResponse, error)
	StartTransaction(ctx context.Context, req *dtos.AktifBankStartTransactionRequest) (*dtos.AktifBankStartTransactionResponse, error)
	Refund(ctx context.Context, req *dtos.AktifBankRefundRequest) (*dtos.AktifBankRefundResponse, error)
	GetCreditStatus(ctx context.Context, req *dtos.AktifBankCreditStatusRequest) (*dtos.AktifBankCreditStatusResponse, error)
}

type repository struct {
	client  *http.Client
	baseURL string
}

func NewRepository(cfg config.AktifBank) Repository {
	baseURL := strings.TrimRight(strings.TrimSpace(cfg.BaseURL), "/")
	timeout := time.Duration(cfg.TimeoutSeconds) * time.Second
	if timeout <= 0 {
		timeout = 30 * time.Second
	}
	return &repository{
		client: &http.Client{
			Timeout: timeout,
		},
		baseURL: baseURL,
	}
}

func (r *repository) Login(ctx context.Context, req *dtos.AktifBankLoginRequest) (*dtos.AktifBankLoginResponse, error) {
	var response dtos.AktifBankLoginResponse
	if err := r.post(ctx, loginPath, req, &response); err != nil {
		return nil, err
	}
	return &response, nil
}

func (r *repository) CheckLimitAvailability(ctx context.Context, req *dtos.AktifBankCheckLimitAvailabilityRequest) (*dtos.AktifBankCheckLimitAvailabilityResponse, error) {
	var response dtos.AktifBankCheckLimitAvailabilityResponse
	if err := r.post(ctx, limitPath, req, &response); err != nil {
		return nil, err
	}
	return &response, nil
}

func (r *repository) GetPaymentPlan(ctx context.Context, req *dtos.AktifBankPaymentPlanRequest) (*dtos.AktifBankPaymentPlanResponse, error) {
	var response dtos.AktifBankPaymentPlanResponse
	if err := r.post(ctx, paymentPlanPath, req, &response); err != nil {
		return nil, err
	}
	return &response, nil
}

func (r *repository) StartTransaction(ctx context.Context, req *dtos.AktifBankStartTransactionRequest) (*dtos.AktifBankStartTransactionResponse, error) {
	var response dtos.AktifBankStartTransactionResponse
	if err := r.post(ctx, startTransactionPath, req, &response); err != nil {
		return nil, err
	}
	return &response, nil
}

func (r *repository) Refund(ctx context.Context, req *dtos.AktifBankRefundRequest) (*dtos.AktifBankRefundResponse, error) {
	var response dtos.AktifBankRefundResponse
	if err := r.post(ctx, refundPath, req, &response); err != nil {
		return nil, err
	}
	return &response, nil
}

func (r *repository) GetCreditStatus(ctx context.Context, req *dtos.AktifBankCreditStatusRequest) (*dtos.AktifBankCreditStatusResponse, error) {
	var response dtos.AktifBankCreditStatusResponse
	if err := r.post(ctx, creditStatusPath, req, &response); err != nil {
		return nil, err
	}
	return &response, nil
}

func (r *repository) post(ctx context.Context, path string, request any, response any) error {
	if r.baseURL == "" {
		return fmt.Errorf("aktifbank base url is not configured")
	}
	url := r.baseURL + path
	body, err := json.Marshal(request)
	if err != nil {
		return fmt.Errorf("marshal aktifbank request: %w", err)
	}
	req, err := http.NewRequestWithContext(ctx, http.MethodPost, url, bytes.NewReader(body))
	if err != nil {
		return fmt.Errorf("build aktifbank request: %w", err)
	}
	req.Header.Set("Content-Type", "application/json")
	resp, err := r.client.Do(req)
	if err != nil {
		return fmt.Errorf("aktifbank http error: %w", err)
	}
	defer resp.Body.Close()
	if resp.StatusCode < http.StatusOK || resp.StatusCode >= http.StatusMultipleChoices {
		msg, _ := io.ReadAll(io.LimitReader(resp.Body, 2048))
		return &APIError{StatusCode: resp.StatusCode, Body: string(msg)}
	}
	if response == nil {
		io.Copy(io.Discard, resp.Body)
		return nil
	}
	decoder := json.NewDecoder(resp.Body)
	decoder.UseNumber()
	if err := decoder.Decode(response); err != nil {
		return fmt.Errorf("decode aktifbank response: %w", err)
	}
	return nil
}

type APIError struct {
	StatusCode int
	Body       string
}

func (e *APIError) Error() string {
	return fmt.Sprintf("aktifbank api error: status=%d body=%s", e.StatusCode, e.Body)
}
