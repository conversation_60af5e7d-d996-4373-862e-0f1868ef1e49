package bankaggregator

import (
	"context"
	"errors"
	"testing"

	"github.com/mono-payments/mono-loan/pkg/dtos"
)

type fakeAdapter struct {
	code string
	name string
	data *BankOfferData
	err  error
}

func (f *fakeAdapter) Code() string { return f.code }
func (f *fakeAdapter) Name() string { return f.name }
func (f *fakeAdapter) Fetch(ctx context.Context, payload BankRequestPayload) (*BankOfferData, error) {
	if f.err != nil {
		return nil, f.err
	}
	return f.data, nil
}

func TestServiceFetchOffersReturnsOrderedResults(t *testing.T) {
	svc := NewService(
		&fakeAdapter{
			code: "aktifbank",
			name: "Aktif Bank",
			data: &BankOfferData{
				Limit: &dtos.NormalizedLimit{AvailableLimit: 15000},
			},
		},
		&fakeAdapter{
			code: "mono-bank",
			name: "Mono Bank",
			data: &BankOfferData{
				Plan: &dtos.NormalizedPaymentPlan{ResponseCode: "00"},
			},
		},
	)

	req := dtos.MultiBankOffersRequest{
		Banks: []dtos.MultiBankRequestItem{
			{BankCode: "aktifbank"},
			{BankCode: "mono-bank"},
		},
	}

	resp, err := svc.FetchOffers(context.Background(), &req)
	if err != nil {
		t.Fatalf("FetchOffers returned error: %v", err)
	}
	if len(resp.Results) != 2 {
		t.Fatalf("expected 2 results, got %d", len(resp.Results))
	}

	if resp.Results[0].BankCode != "aktifbank" {
		t.Fatalf("expected first bank code aktifbank, got %s", resp.Results[0].BankCode)
	}
	if resp.Results[0].Limit == nil || resp.Results[0].Limit.AvailableLimit != 15000 {
		t.Fatalf("unexpected limit data: %+v", resp.Results[0].Limit)
	}
	if resp.Results[1].BankCode != "mono-bank" {
		t.Fatalf("expected second bank code mono-bank, got %s", resp.Results[1].BankCode)
	}
	if resp.Results[1].Plan == nil || resp.Results[1].Plan.ResponseCode != "00" {
		t.Fatalf("unexpected plan data: %+v", resp.Results[1].Plan)
	}
}

func TestServiceFetchOffersHandlesAdapterErrors(t *testing.T) {
	svc := NewService(
		&fakeAdapter{
			code: "aktifbank",
			name: "Aktif Bank",
			err:  errors.New("boom"),
		},
	)

	req := dtos.MultiBankOffersRequest{
		Banks: []dtos.MultiBankRequestItem{
			{BankCode: "aktifbank"},
			{BankCode: "nonexistent"},
		},
	}

	resp, err := svc.FetchOffers(context.Background(), &req)
	if err != nil {
		t.Fatalf("FetchOffers returned error: %v", err)
	}
	if len(resp.Results) != 2 {
		t.Fatalf("expected 2 results, got %d", len(resp.Results))
	}
	if resp.Results[0].Error == nil || resp.Results[0].Error.Message != "boom" {
		t.Fatalf("expected adapter error boom, got %+v", resp.Results[0].Error)
	}
	if resp.Results[1].Error == nil || resp.Results[1].Error.Message == "" {
		t.Fatalf("expected missing adapter error, got %+v", resp.Results[1].Error)
	}
}
