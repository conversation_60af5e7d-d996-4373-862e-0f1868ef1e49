package bankaggregator

import (
	"context"
	"encoding/json"

	"github.com/mono-payments/mono-loan/pkg/dtos"
)

type Adapter interface {
	Code() string
	Name() string
	Fetch(ctx context.Context, payload BankRequestPayload) (*BankOfferData, error)
}

type BankRequestPayload struct {
	Limit       json.RawMessage
	PaymentPlan json.RawMessage
}

type BankOfferData struct {
	Limit *dtos.NormalizedLimit
	Plan  *dtos.NormalizedPaymentPlan
}
