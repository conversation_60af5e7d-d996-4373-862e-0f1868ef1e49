package bankaggregator

import (
	"context"
	"fmt"
	"strings"
	"sync"

	"github.com/mono-payments/mono-loan/pkg/dtos"
	"golang.org/x/sync/errgroup"
)

type Service interface {
	FetchOffers(ctx context.Context, req *dtos.MultiBankOffersRequest) (*dtos.MultiBankOffersResponse, error)
}

type service struct {
	adapters map[string]Adapter
}

func NewService(adapters ...Adapter) Service {
	registry := make(map[string]Adapter, len(adapters))
	for _, adapter := range adapters {
		if adapter == nullAdapter {
			continue
		}
		code := strings.ToLower(strings.TrimSpace(adapter.Code()))
		if code == "" {
			continue
		}
		registry[code] = adapter
	}
	return &service{adapters: registry}
}

var nullAdapter Adapter

func (s *service) FetchOffers(ctx context.Context, req *dtos.MultiBankOffersRequest) (*dtos.MultiBankOffersResponse, error) {
	if req == nil {
		return nil, fmt.Errorf("request cannot be nil")
	}
	if len(req.Banks) == 0 {
		return nil, fmt.Errorf("at least one bank is required")
	}

	results := make([]dtos.BankOfferResult, len(req.Banks))

	group, ctx := errgroup.WithContext(ctx)
	var mu sync.Mutex

	for idx, bankReq := range req.Banks {
		index := idx
		payload := BankRequestPayload{
			Limit:       bankReq.Limit,
			PaymentPlan: bankReq.PaymentPlan,
		}
		adapter, ok := s.adapters[strings.ToLower(bankReq.BankCode)]
		if !ok {
			results[index] = dtos.BankOfferResult{
				BankCode: bankReq.BankCode,
				BankName: bankReq.BankCode,
				Error:    &dtos.BankError{Message: fmt.Sprintf("adapter not found for bank %s", bankReq.BankCode)},
			}
			continue
		}

		payloadCopy := payload
		adapterRef := adapter
		group.Go(func() error {
			data, err := adapterRef.Fetch(ctx, payloadCopy)
			result := dtos.BankOfferResult{
				BankCode: adapterRef.Code(),
				BankName: adapterRef.Name(),
			}
			if err != nil {
				result.Error = &dtos.BankError{Message: err.Error()}
			} else if data != nil {
				result.Limit = data.Limit
				result.Plan = data.Plan
			}
			mu.Lock()
			results[index] = result
			mu.Unlock()
			return nil
		})
	}

	if err := group.Wait(); err != nil {
		return nil, err
	}

	return &dtos.MultiBankOffersResponse{Results: results}, nil
}
