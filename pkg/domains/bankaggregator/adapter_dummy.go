package bankaggregator

import (
	"context"

	"github.com/mono-payments/mono-loan/pkg/dtos"
)

type monoBankAdapter struct{}

func NewMonoBankAdapter() Adapter {
	return &monoBankAdapter{}
}

func (m *monoBankAdapter) Code() string {
	return "mono-bank"
}

func (m *monoBankAdapter) Name() string {
	return "Mono Bank"
}

func (m *monoBankAdapter) Fetch(ctx context.Context, payload BankRequestPayload) (*BankOfferData, error) {
	_ = ctx

	limit := &dtos.NormalizedLimit{
		PreApprovedApplicationID: "DUMMY-APP-123456",
		AvailableLimit:           75000,
		MinInterestRate:          1.79,
		MaxInterestRate:          2.15,
		MaxTerm:                  36,
		MinAmount:                5000,
		InstallmentAmountForMax:  2875.42,
		IsNewCustomer:            true,
		ResponseCode:             "00",
		ResponseMessage:          "Approved",
		IsSuccess:                true,
	}

	plan := &dtos.NormalizedPaymentPlan{
		Type:            "STANDARD",
		ResponseCode:    "00",
		ResponseMessage: "Success",
		Options: []dtos.NormalizedPaymentPlanOption{
			{
				Term:                    12,
				InstallmentAmount:       6750.55,
				TotalPaymentAmount:      81006.6,
				InterestRate:            1.89,
				AnnualEffectiveInterest: 24.5,
				LoanAmount:              75000,
				CreditInsuranceFee:      350.25,
				CreditUsageFee:          150,
			},
			{
				Term:                    24,
				InstallmentAmount:       3750.22,
				TotalPaymentAmount:      90005.28,
				InterestRate:            1.95,
				AnnualEffectiveInterest: 25.1,
				LoanAmount:              75000,
				CreditInsuranceFee:      450.80,
				CreditUsageFee:          150,
			},
		},
	}

	return &BankOfferData{Limit: limit, Plan: plan}, nil
}
