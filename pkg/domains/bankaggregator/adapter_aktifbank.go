package bankaggregator

import (
	"context"
	"encoding/json"
	"fmt"

	"github.com/mono-payments/mono-loan/pkg/domains/aktifbank"
	"github.com/mono-payments/mono-loan/pkg/dtos"
)

type aktifbankAdapter struct {
	service aktifbank.Service
}

func NewAktifbankAdapter(service aktifbank.Service) Adapter {
	if service == nil {
		return nullAdapter
	}
	return &aktifbankAdapter{service: service}
}

func (a *aktifbankAdapter) Code() string {
	return "aktifbank"
}

func (a *aktifbankAdapter) Name() string {
	return "Aktif Bank"
}

func (a *aktifbankAdapter) Fetch(ctx context.Context, payload BankRequestPayload) (*BankOfferData, error) {
	if len(payload.Limit) == 0 {
		return nil, fmt.Errorf("aktifbank adapter requires limit payload")
	}

	var limitReq dtos.AktifBankCheckLimitAvailabilityRequest
	if err := json.Unmarshal(payload.Limit, &limitReq); err != nil {
		return nil, fmt.Errorf("aktifbank limit payload decode: %w", err)
	}

	limitResp, err := a.service.CheckLimitAvailability(ctx, &limitReq)
	if err != nil {
		return nil, err
	}

	data := &BankOfferData{Limit: normalizeAktifBankLimit(limitResp)}

	if len(payload.PaymentPlan) > 0 {
		var planReq dtos.AktifBankPaymentPlanRequest
		if err := json.Unmarshal(payload.PaymentPlan, &planReq); err != nil {
			return data, fmt.Errorf("aktifbank payment plan payload decode: %w", err)
		}
		planResp, err := a.service.GetPaymentPlan(ctx, &planReq)
		if err != nil {
			return data, err
		}
		data.Plan = normalizeAktifBankPlan(planResp)
	}

	return data, nil
}

func normalizeAktifBankLimit(res *dtos.AktifBankCheckLimitAvailabilityResponse) *dtos.NormalizedLimit {
	if res == nil {
		return nil
	}
	return &dtos.NormalizedLimit{
		PreApprovedApplicationID: res.PreApprovedApplicationID.String(),
		AvailableLimit:           numberToFloat(res.AvailableLimit),
		MinInterestRate:          numberToFloat(res.MinInterestRate),
		MaxInterestRate:          numberToFloat(res.MaxInterestRate),
		MaxTerm:                  numberToInt(res.MaxTerm),
		MinAmount:                numberToFloat(res.MinAmount),
		InstallmentAmountForMax:  numberToFloat(res.InstallmentAmountForMaxTerm),
		IsNewCustomer:            res.IsNewCustomer,
		ResponseCode:             res.ResponseCode,
		ResponseMessage:          res.ResponseMessage,
		IsSuccess:                res.IsSuccess,
	}
}

func normalizeAktifBankPlan(res *dtos.AktifBankPaymentPlanResponse) *dtos.NormalizedPaymentPlan {
	if res == nil {
		return nil
	}
	options := make([]dtos.NormalizedPaymentPlanOption, 0, len(res.PaymentPlan))
	for _, item := range res.PaymentPlan {
		options = append(options, dtos.NormalizedPaymentPlanOption{
			Term:                    numberToInt(item.Term),
			InstallmentAmount:       numberToFloat(item.InstallmentAmount),
			TotalPaymentAmount:      numberToFloat(item.TotalPaymentAmount),
			InterestRate:            numberToFloat(item.InterestRate),
			AnnualEffectiveInterest: numberToFloat(item.AnnualEffectiveInterest),
			LoanAmount:              numberToFloat(item.LoanAmount),
			CreditInsuranceFee:      numberToFloat(item.CreditInsuranceFee),
			CreditUsageFee:          numberToFloat(item.CreditUsageFee),
		})
	}
	return &dtos.NormalizedPaymentPlan{
		Type:            res.Type,
		ResponseCode:    res.ResponseCode,
		ResponseMessage: res.ResponseMessage,
		Options:         options,
	}
}
