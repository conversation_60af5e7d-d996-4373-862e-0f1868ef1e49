// Package docs Code generated by swaggo/swag. DO NOT EDIT
package docs

import "github.com/swaggo/swag"

const docTemplate = `{
    "schemes": {{ marshal .Schemes }},
    "swagger": "2.0",
    "info": {
        "description": "{{escape .Description}}",
        "title": "{{.Title}}",
        "contact": {},
        "version": "{{.Version}}"
    },
    "host": "{{.Host}}",
    "basePath": "{{.BasePath}}",
    "paths": {
        "/aktifbank/applications/status": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Başvuru veya iade durumunu sorgular",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "AktifBank"
                ],
                "summary": "Aktif Bank kredi durumu",
                "parameters": [
                    {
                        "description": "Credit status request",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dtos.AktifBankCreditStatusRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/dtos.AktifBankCreditStatusResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "502": {
                        "description": "Bad Gateway",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            }
        },
        "/aktifbank/limits": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Müşteri limit ve vade bilgilerini döner",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "AktifBank"
                ],
                "summary": "Aktif Bank limit sorgulama",
                "parameters": [
                    {
                        "description": "Limit request",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dtos.AktifBankCheckLimitAvailabilityRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/dtos.AktifBankCheckLimitAvailabilityResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "502": {
                        "description": "Bad Gateway",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            }
        },
        "/aktifbank/login": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Login servisi ile session ID üretir",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "AktifBank"
                ],
                "summary": "Aktif Bank oturum açma",
                "parameters": [
                    {
                        "description": "Login request",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dtos.AktifBankLoginRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/dtos.AktifBankLoginResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "502": {
                        "description": "Bad Gateway",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            }
        },
        "/aktifbank/payment-plans": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Verilen kredi tutarı için ödeme planı listeler",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "AktifBank"
                ],
                "summary": "Aktif Bank ödeme planları",
                "parameters": [
                    {
                        "description": "Payment plan request",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dtos.AktifBankPaymentPlanRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/dtos.AktifBankPaymentPlanResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "502": {
                        "description": "Bad Gateway",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            }
        },
        "/aktifbank/refunds": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Belirtilen başvuru için kredi iade talebi oluşturur",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "AktifBank"
                ],
                "summary": "Aktif Bank kredi iadesi",
                "parameters": [
                    {
                        "description": "Refund request",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dtos.AktifBankRefundRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/dtos.AktifBankRefundResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "502": {
                        "description": "Bad Gateway",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            }
        },
        "/aktifbank/transactions": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Banka başvurusunu başlatır ve yönlendirme linki döner",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "AktifBank"
                ],
                "summary": "Aktif Bank kredi başvuru başlatma",
                "parameters": [
                    {
                        "description": "Start transaction request",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dtos.AktifBankStartTransactionRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/dtos.AktifBankStartTransactionResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "502": {
                        "description": "Bad Gateway",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            }
        }
    },
    "definitions": {
        "dtos.AktifBankCheckLimitAvailabilityRequest": {
            "type": "object",
            "required": [
                "BirthDate",
                "CLIENT_SESSION_ID",
                "GsmNumber",
                "NationalIdentityNumber",
                "ReferenceId",
                "SESSION_ID"
            ],
            "properties": {
                "BirthDate": {
                    "type": "string"
                },
                "CLIENT_SESSION_ID": {
                    "type": "string"
                },
                "CLIENT_TRX_ID_RESERVED": {
                    "type": "string"
                },
                "GsmNumber": {
                    "type": "string"
                },
                "MonthlyIncome": {
                    "type": "number"
                },
                "NationalIdentityNumber": {
                    "type": "string"
                },
                "ReferenceId": {
                    "type": "string"
                },
                "SESSION_ID": {
                    "type": "string"
                },
                "TotalPrice": {
                    "type": "number"
                }
            }
        },
        "dtos.AktifBankCheckLimitAvailabilityResponse": {
            "type": "object",
            "properties": {
                "AvailableLimit": {
                    "type": "string"
                },
                "CORE_TRX_ID_RESERVED": {
                    "type": "string"
                },
                "InstallmentAmountForMaxTerm": {
                    "type": "string"
                },
                "IsNewCustomer": {
                    "type": "boolean"
                },
                "IsSuccess": {
                    "type": "boolean"
                },
                "MaxInterestRate": {
                    "type": "string"
                },
                "MaxTerm": {
                    "type": "string"
                },
                "MinAmount": {
                    "type": "string"
                },
                "MinInterestRate": {
                    "type": "string"
                },
                "PreApprovedApplicationId": {
                    "type": "string"
                },
                "ResponseCode": {
                    "type": "string"
                },
                "ResponseMessage": {
                    "type": "string"
                }
            }
        },
        "dtos.AktifBankCreditStatusAllocation": {
            "type": "object",
            "properties": {
                "AllocationDate": {
                    "type": "string"
                },
                "IsSuccess": {
                    "type": "boolean"
                }
            }
        },
        "dtos.AktifBankCreditStatusPaymentPlan": {
            "type": "object",
            "properties": {
                "AnnualEffectiveInterestRate": {
                    "type": "string"
                },
                "CreditInsuranceFee": {
                    "type": "string"
                },
                "CreditUsageFee": {
                    "type": "string"
                },
                "InstallAmount": {
                    "type": "string"
                },
                "InterestRate": {
                    "type": "string"
                },
                "LendingAmount": {
                    "type": "string"
                },
                "Term": {
                    "type": "string"
                },
                "TotalPaymentAmount": {
                    "type": "string"
                },
                "UsedAmount": {
                    "type": "string"
                }
            }
        },
        "dtos.AktifBankCreditStatusRefundInformation": {
            "type": "object",
            "properties": {
                "TotalRefundAmount": {
                    "type": "string"
                }
            }
        },
        "dtos.AktifBankCreditStatusRequest": {
            "type": "object",
            "required": [
                "ApplicationId",
                "CLIENT_SESSION_ID",
                "SESSION_ID"
            ],
            "properties": {
                "ApplicationId": {
                    "type": "string"
                },
                "CLIENT_SESSION_ID": {
                    "type": "string"
                },
                "Date": {
                    "type": "string"
                },
                "IdentityNumber": {
                    "type": "string"
                },
                "SESSION_ID": {
                    "type": "string"
                }
            }
        },
        "dtos.AktifBankCreditStatusResponse": {
            "type": "object",
            "properties": {
                "AllocationInformation": {
                    "$ref": "#/definitions/dtos.AktifBankCreditStatusAllocation"
                },
                "ApplicationDate": {
                    "type": "string"
                },
                "ApplicationId": {
                    "type": "string"
                },
                "CORE_TRX_ID_RESERVED": {
                    "type": "string"
                },
                "Iban": {
                    "type": "string"
                },
                "IsApproved": {
                    "type": "boolean"
                },
                "IsDobCompleted": {
                    "type": "boolean"
                },
                "IsNewCustomer": {
                    "type": "boolean"
                },
                "NationalIdentityNumber": {
                    "type": "string"
                },
                "PaymentPlan": {
                    "$ref": "#/definitions/dtos.AktifBankCreditStatusPaymentPlan"
                },
                "RefundInformation": {
                    "$ref": "#/definitions/dtos.AktifBankCreditStatusRefundInformation"
                },
                "ResponseCode": {
                    "type": "string"
                },
                "ResponseMessage": {
                    "type": "string"
                }
            }
        },
        "dtos.AktifBankLoginRequest": {
            "type": "object",
            "required": [
                "CLIENT_SESSION_ID"
            ],
            "properties": {
                "CLIENT_SESSION_ID": {
                    "type": "string"
                },
                "LANGUAGE": {
                    "type": "string"
                },
                "SUB_CHANNEL_RESERVED": {
                    "type": "string"
                }
            }
        },
        "dtos.AktifBankLoginResponse": {
            "type": "object",
            "properties": {
                "SESSION_ID": {
                    "type": "string"
                }
            }
        },
        "dtos.AktifBankPaymentPlanOption": {
            "type": "object",
            "properties": {
                "AnnualEffectiveInterestRate": {
                    "type": "string"
                },
                "CreditInsuranceFee": {
                    "type": "string"
                },
                "CreditUsageFee": {
                    "type": "string"
                },
                "InstallmentAmount": {
                    "type": "string"
                },
                "InterestRate": {
                    "type": "string"
                },
                "LoanAmount": {
                    "type": "string"
                },
                "PaymentDate": {
                    "type": "string"
                },
                "Term": {
                    "type": "string"
                },
                "TotalPaymentAmount": {
                    "type": "string"
                }
            }
        },
        "dtos.AktifBankPaymentPlanRequest": {
            "type": "object",
            "required": [
                "CLIENT_SESSION_ID",
                "NationalIdentityNumber",
                "PreApprovedApplicationId",
                "ReferenceId",
                "SESSION_ID",
                "TotalPrice"
            ],
            "properties": {
                "CLIENT_SESSION_ID": {
                    "type": "string"
                },
                "CLIENT_TRX_ID_RESERVED": {
                    "type": "string"
                },
                "InstallmentDate": {
                    "type": "string"
                },
                "NationalIdentityNumber": {
                    "type": "string"
                },
                "PreApprovedApplicationId": {
                    "type": "string"
                },
                "ReferenceId": {
                    "type": "string"
                },
                "SESSION_ID": {
                    "type": "string"
                },
                "TotalPrice": {
                    "type": "number"
                }
            }
        },
        "dtos.AktifBankPaymentPlanResponse": {
            "type": "object",
            "properties": {
                "CORE_TRX_ID_RESERVED": {
                    "type": "string"
                },
                "PaymentPlan": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/dtos.AktifBankPaymentPlanOption"
                    }
                },
                "ResponseCode": {
                    "type": "string"
                },
                "ResponseMessage": {
                    "type": "string"
                },
                "Type": {
                    "type": "string"
                }
            }
        },
        "dtos.AktifBankRefundRequest": {
            "type": "object",
            "required": [
                "ApplicationId",
                "CLIENT_SESSION_ID",
                "ReferenceId",
                "RefundAmount",
                "RefundType",
                "SESSION_ID"
            ],
            "properties": {
                "ApplicationDate": {
                    "type": "string"
                },
                "ApplicationId": {
                    "type": "string"
                },
                "CLIENT_SESSION_ID": {
                    "type": "string"
                },
                "CLIENT_TRX_ID_RESERVED": {
                    "type": "string"
                },
                "ReferenceId": {
                    "type": "string"
                },
                "RefundAmount": {
                    "type": "number"
                },
                "RefundType": {
                    "type": "string",
                    "enum": [
                        "FULL",
                        "PARTIAL"
                    ]
                },
                "SESSION_ID": {
                    "type": "string"
                }
            }
        },
        "dtos.AktifBankRefundResponse": {
            "type": "object",
            "properties": {
                "BankReferenceId": {
                    "type": "string"
                },
                "CORE_TRX_ID_RESERVED": {
                    "type": "string"
                },
                "IsSuccess": {
                    "type": "boolean"
                },
                "ResponseCode": {
                    "type": "string"
                },
                "ResponseMessage": {
                    "type": "string"
                }
            }
        },
        "dtos.AktifBankStartTransactionRequest": {
            "type": "object",
            "required": [
                "CLIENT_SESSION_ID",
                "CallbackUrl",
                "GsmNumber",
                "NationalIdentityNumber",
                "PreApprovedApplicationId",
                "ReferenceId",
                "SESSION_ID",
                "TimeToLive",
                "TotalPrice",
                "TotalTerm"
            ],
            "properties": {
                "CLIENT_SESSION_ID": {
                    "type": "string"
                },
                "CLIENT_TRX_ID_RESERVED": {
                    "type": "string"
                },
                "CallbackUrl": {
                    "type": "string"
                },
                "GsmNumber": {
                    "type": "string"
                },
                "NationalIdentityNumber": {
                    "type": "string"
                },
                "PlatformId": {
                    "type": "integer"
                },
                "PreApprovedApplicationId": {
                    "type": "string"
                },
                "ReferenceId": {
                    "type": "string"
                },
                "SESSION_ID": {
                    "type": "string"
                },
                "TimeToLive": {
                    "type": "integer"
                },
                "TotalPrice": {
                    "type": "number"
                },
                "TotalTerm": {
                    "type": "integer"
                },
                "Type": {
                    "type": "string"
                }
            }
        },
        "dtos.AktifBankStartTransactionResponse": {
            "type": "object",
            "properties": {
                "ApplicationDate": {
                    "type": "string"
                },
                "ApplicationId": {
                    "type": "string"
                },
                "BankRedirectionUrl": {
                    "type": "string"
                },
                "CORE_TRX_ID_RESERVED": {
                    "type": "string"
                },
                "IsApproved": {
                    "type": "boolean"
                },
                "ResponseCode": {
                    "type": "string"
                },
                "ResponseMessage": {
                    "type": "string"
                }
            }
        }
    },
    "securityDefinitions": {
        "BearerAuth": {
            "type": "apiKey",
            "name": "Authorization",
            "in": "header"
        }
    }
}`

// SwaggerInfo holds exported Swagger Info so clients can modify it
var SwaggerInfo = &swag.Spec{
	Version:          "1.0",
	Host:             "localhost:8000",
	BasePath:         "/api/v1",
	Schemes:          []string{"http", "https"},
	Title:            "Mono Loan API",
	Description:      "Mono Loan API Documentation",
	InfoInstanceName: "swagger",
	SwaggerTemplate:  docTemplate,
	LeftDelim:        "{{",
	RightDelim:       "}}",
}

func init() {
	swag.Register(SwaggerInfo.InstanceName(), SwaggerInfo)
}
